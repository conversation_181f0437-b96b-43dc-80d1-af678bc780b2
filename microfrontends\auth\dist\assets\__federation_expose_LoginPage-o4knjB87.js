import { importShared } from './__federation_fn_import-DL9kAWwu.js';
import { x as createTheme, U as createSvgIcon, j as jsxRuntimeExports, ad as ThemeProvider } from './createSvgIcon-BvEkhiOn.js';

const {styled,Button,Paper,Card,Box,Typography,CardContent,CardActions,Grid,Container,AppBar,Toolbar,TextField,Alert,InputAdornment,IconButton,Link,Divider,CircularProgress} = await importShared('@mui/material');


// Custom color palette
const customColors = {
    primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
    },
    secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
    },
    success: {
        main: '#2e7d32',
        light: '#4caf50',
        dark: '#1b5e20',
    },
    warning: {
        main: '#ed6c02',
        light: '#ff9800',
        dark: '#e65100',
    },
    error: {
        main: '#d32f2f',
        light: '#ef5350',
        dark: '#c62828',
    },
};
// Base theme configuration
const baseThemeOptions = {
    palette: {
        ...customColors,
    },
    typography: {
        fontFamily: [
            'Roboto',
            '-apple-system',
            'BlinkMacSystemFont',
            '"Segoe UI"',
            'Arial',
            'sans-serif',
        ].join(','),
    },
    shape: {
        borderRadius: 8,
    },
    spacing: 8,
};
// Component overrides for customized components
const componentOverrides = {
    MuiButton: {
        styleOverrides: {
            root: {
                textTransform: 'none',
                borderRadius: 8,
                fontWeight: 500,
                padding: '8px 16px',
                boxShadow: 'none',
                '&:hover': {
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                },
            },
            contained: {
                '&:hover': {
                    boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                },
            },
        },
    },
    MuiPaper: {
        styleOverrides: {
            root: {
                borderRadius: 12,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
            elevation1: {
                boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
            },
            elevation2: {
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
            elevation3: {
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            },
        },
    },
    MuiCard: {
        styleOverrides: {
            root: {
                borderRadius: 12,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                '&:hover': {
                    boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
                },
            },
        },
    },
};
// Create the theme
const appTheme = createTheme({
    ...baseThemeOptions,
    components: componentOverrides,
});

// Custom styled button variants
const StyledButton = styled(Button)(({ theme, customVariant }) => ({
    // Base styles
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 500,
    padding: '10px 20px',
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'gradient' && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        color: 'white',
        border: 'none',
        '&:hover': {
            background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
        },
    }),
    ...(customVariant === 'outlined-hover' && {
        border: `2px solid ${theme.palette.primary.main}`,
        color: theme.palette.primary.main,
        backgroundColor: 'transparent',
        '&:hover': {
            backgroundColor: theme.palette.primary.main,
            color: 'white',
            transform: 'scale(1.05)',
        },
    }),
    ...(customVariant === 'soft' && {
        backgroundColor: theme.palette.primary.light + '20', // 20% opacity
        color: theme.palette.primary.main,
        border: 'none',
        '&:hover': {
            backgroundColor: theme.palette.primary.light + '40', // 40% opacity
            transform: 'translateY(-1px)',
        },
    }),
}));
const CustomButton = ({ variant = 'contained', loading = false, icon, children, disabled, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['gradient', 'outlined-hover', 'soft'].includes(variant);
    const muiVariant = isCustomVariant ? 'contained' : variant;
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntimeExports.jsx(StyledButton, { variant: muiVariant, customVariant: customVariant, disabled: disabled || loading, startIcon: loading ? undefined : icon, ...props, children: loading ? (jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [jsxRuntimeExports.jsx("span", { style: { marginRight: 8 }, children: "\u23F3" }), "Loading..."] })) : (children) }));
};

// Custom styled paper variants
const StyledPaper = styled(Paper)(({ theme, customVariant }) => ({
    // Base styles
    borderRadius: 12,
    padding: theme.spacing(3),
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'bordered' && {
        border: `2px solid ${theme.palette.primary.main}`,
        boxShadow: 'none',
        '&:hover': {
            borderColor: theme.palette.primary.dark,
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        },
    }),
    ...(customVariant === 'gradient' && {
        background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${theme.palette.primary.light}10)`,
        border: `1px solid ${theme.palette.primary.light}30`,
        '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
        },
    }),
    ...(customVariant === 'glass' && {
        backgroundColor: theme.palette.background.paper + 'CC', // 80% opacity
        backdropFilter: 'blur(10px)',
        border: `1px solid ${theme.palette.divider}`,
        '&:hover': {
            backgroundColor: theme.palette.background.paper + 'DD', // 85% opacity
            transform: 'translateY(-1px)',
        },
    }),
    ...(customVariant === 'elevated' && {
        boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        '&:hover': {
            boxShadow: '0 12px 40px rgba(0,0,0,0.18)',
            transform: 'translateY(-4px)',
        },
    }),
}));
const CustomPaper = ({ variant = 'elevation', padding, hover = true, children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['bordered', 'gradient', 'glass', 'elevated'].includes(variant);
    const muiVariant = isCustomVariant ? 'elevation' : variant;
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntimeExports.jsx(StyledPaper, { variant: muiVariant, customVariant: customVariant, sx: {
            ...(padding && { padding }),
            ...(hover && {
                cursor: 'pointer',
                '&:hover': {
                    transform: 'translateY(-2px)',
                },
            }),
            ...sx,
        }, ...props, children: children }));
};

// Custom styled card variants
const StyledCard = styled(Card)(({ theme, customVariant }) => ({
    // Base styles
    borderRadius: 12,
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    // Custom variant styles
    ...(customVariant === 'interactive' && {
        '&:hover': {
            transform: 'translateY(-8px)',
            boxShadow: '0 12px 32px rgba(0,0,0,0.2)',
        },
    }),
    ...(customVariant === 'feature' && {
        border: `2px solid ${theme.palette.primary.light}`,
        backgroundColor: theme.palette.primary.light + '05',
        '&:hover': {
            borderColor: theme.palette.primary.main,
            backgroundColor: theme.palette.primary.light + '10',
            transform: 'scale(1.02)',
        },
    }),
    ...(customVariant === 'minimal' && {
        boxShadow: 'none',
        border: `1px solid ${theme.palette.divider}`,
        '&:hover': {
            borderColor: theme.palette.primary.main,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
    }),
}));
const CustomCard = ({ variant = 'elevation', title, subtitle, actions, icon, headerColor, children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['interactive', 'feature', 'minimal'].includes(variant);
    const muiVariant = isCustomVariant ? 'elevation' : variant;
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntimeExports.jsxs(StyledCard, { variant: muiVariant, customVariant: customVariant, sx: sx, ...props, children: [(title || subtitle || icon) && (jsxRuntimeExports.jsx(Box, { sx: {
                    p: 2,
                    pb: title || subtitle ? 1 : 2,
                    ...(headerColor && {
                        backgroundColor: headerColor,
                        color: 'white',
                        '&:first-of-type': {
                            borderRadius: '12px 12px 0 0',
                        },
                    }),
                }, children: jsxRuntimeExports.jsxs(Box, { sx: { display: 'flex', alignItems: 'center', gap: 2 }, children: [icon && (jsxRuntimeExports.jsx(Box, { sx: {
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 40,
                                height: 40,
                                borderRadius: 2,
                                backgroundColor: headerColor ? 'rgba(255,255,255,0.2)' : 'primary.light',
                                color: headerColor ? 'white' : 'primary.main',
                            }, children: icon })), jsxRuntimeExports.jsxs(Box, { sx: { flexGrow: 1 }, children: [title && (jsxRuntimeExports.jsx(Typography, { variant: "h6", component: "h2", gutterBottom: !!subtitle, children: title })), subtitle && (jsxRuntimeExports.jsx(Typography, { variant: "body2", color: "text.secondary", children: subtitle }))] })] }) })), jsxRuntimeExports.jsx(CardContent, { sx: { pt: (title || subtitle || icon) ? 1 : 2 }, children: children }), actions && (jsxRuntimeExports.jsx(CardActions, { sx: { p: 2, pt: 0 }, children: actions }))] }));
};

// Custom styled box variants
const StyledBox = styled(Box)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'flex-center' && {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    }),
    ...(customVariant === 'flex-column' && {
        display: 'flex',
        flexDirection: 'column',
    }),
    ...(customVariant === 'flex-between' && {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    }),
    ...(customVariant === 'grid-container' && {
        display: 'grid',
        gap: theme.spacing(2),
    }),
}));
const CustomBox = ({ variant = 'default', children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['flex-center', 'flex-column', 'flex-between', 'grid-container'].includes(variant);
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntimeExports.jsx(StyledBox, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled grid variants
styled(Grid)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'responsive-cards' && {
        '& > .MuiGrid-item': {
            display: 'flex',
            '& > *': {
                width: '100%',
            },
        },
    }),
    ...(customVariant === 'equal-height' && {
        '& > .MuiGrid-item': {
            display: 'flex',
            alignItems: 'stretch',
            '& > *': {
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
            },
        },
    }),
    ...(customVariant === 'masonry' && {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: theme.spacing(3),
        alignItems: 'start',
    }),
}));

// Custom styled typography variants
const StyledTypography = styled(Typography, {
    shouldForwardProp: (prop) => prop !== 'customVariant',
})(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'gradient-text' && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        fontWeight: 600,
    }),
    ...(customVariant === 'highlight' && {
        backgroundColor: theme.palette.primary.light + '20',
        padding: '2px 8px',
        borderRadius: 4,
        fontWeight: 500,
        color: theme.palette.primary.dark,
    }),
    ...(customVariant === 'code' && {
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        backgroundColor: theme.palette.grey[100],
        padding: '2px 6px',
        borderRadius: 4,
        fontSize: '0.875em',
        border: `1px solid ${theme.palette.grey[300]}`,
    }),
    ...(customVariant === 'caption-bold' && {
        fontWeight: 600,
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        fontSize: '0.75rem',
    }),
}));
const CustomTypography = ({ customVariant, children, sx, ...props }) => {
    return (jsxRuntimeExports.jsx(StyledTypography, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled container variants
const StyledContainer = styled(Container)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'centered' && {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        textAlign: 'center',
    }),
    ...(customVariant === 'full-height' && {
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
    }),
    ...(customVariant === 'padded' && {
        paddingTop: theme.spacing(4),
        paddingBottom: theme.spacing(4),
        [theme.breakpoints.up('md')]: {
            paddingTop: theme.spacing(6),
            paddingBottom: theme.spacing(6),
        },
    }),
    ...(customVariant === 'narrow' && {
        maxWidth: '600px !important',
    }),
}));
const CustomContainer = ({ variant = 'default', children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['centered', 'full-height', 'padded', 'narrow'].includes(variant);
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntimeExports.jsx(StyledContainer, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled app bar variants
styled(AppBar)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    // Custom variant styles
    ...(customVariant === 'gradient' && {
        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
        '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(45deg, rgba(255,255,255,0.1), transparent)',
            pointerEvents: 'none',
        },
    }),
    ...(customVariant === 'glass' && {
        backgroundColor: theme.palette.background.paper + 'CC',
        backdropFilter: 'blur(10px)',
        border: `1px solid ${theme.palette.divider}`,
        color: theme.palette.text.primary,
    }),
    ...(customVariant === 'minimal' && {
        backgroundColor: 'transparent',
        boxShadow: 'none',
        borderBottom: `1px solid ${theme.palette.divider}`,
        color: theme.palette.text.primary,
    }),
    ...(customVariant === 'elevated' && {
        boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
        '&:hover': {
            boxShadow: '0 6px 20px rgba(0,0,0,0.2)',
        },
    }),
}));

const CustomTextField = (props) => {
    return jsxRuntimeExports.jsx(TextField, { ...props });
};

const CustomAlert = (props) => {
    return jsxRuntimeExports.jsx(Alert, { ...props });
};

const CustomInputAdornment = (props) => {
    return jsxRuntimeExports.jsx(InputAdornment, { ...props });
};

const CustomIconButton = (props) => {
    return jsxRuntimeExports.jsx(IconButton, { ...props });
};

const CustomLink = (props) => {
    return jsxRuntimeExports.jsx(Link, { ...props });
};

const CustomDivider = (props) => {
    return jsxRuntimeExports.jsx(Divider, { ...props });
};

const CustomThemeProvider = ({ theme, children }) => {
    return jsxRuntimeExports.jsx(ThemeProvider, { theme: theme, children: children });
};

createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"
}), 'Dashboard');

var Email = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"
}), 'Email');

var GitHubIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12 1.27a11 11 0 00-3.48 21.46c.55.09.73-.28.73-.55v-1.84c-3.03.64-3.67-1.46-3.67-1.46-.55-1.29-1.28-1.65-1.28-1.65-.92-.65.1-.65.1-.65 1.1 0 1.73 1.1 1.73 1.1.92 1.65 2.57 1.2 3.21.92a2 2 0 01.64-1.47c-2.47-.27-5.04-1.19-5.04-5.5 0-1.1.46-2.1 1.2-2.84a3.76 3.76 0 010-2.93s.91-.28 3.11 1.1c1.8-.49 3.7-.49 5.5 0 2.1-1.38 3.02-1.1 3.02-1.1a3.76 3.76 0 010 2.93c.83.74 1.2 1.74 1.2 2.94 0 4.21-2.57 5.13-5.04 5.4.45.37.82.92.82 2.02v3.03c0 .27.1.64.73.55A11 11 0 0012 1.27"
}), 'GitHub');

var GoogleIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"
}), 'Google');

var Lock = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2m-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1s3.1 1.39 3.1 3.1z"
}), 'Lock');

var LoginIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M11 7 9.6 8.4l2.6 2.6H2v2h10.2l-2.6 2.6L11 17l5-5zm9 12h-8v2h8c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-8v2h8z"
}), 'Login');

createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"
}), 'Person');

createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11z"
}), 'Security');

var Visibility = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"
}), 'Visibility');

var VisibilityOff = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"
}), 'VisibilityOff');

const CustomVisibilityIcon = (props) => {
    return jsxRuntimeExports.jsx(Visibility, { ...props });
};
const CustomVisibilityOffIcon = (props) => {
    return jsxRuntimeExports.jsx(VisibilityOff, { ...props });
};
const CustomEmailIcon = (props) => {
    return jsxRuntimeExports.jsx(Email, { ...props });
};
const CustomLockIcon = (props) => {
    return jsxRuntimeExports.jsx(Lock, { ...props });
};
const CustomLoginIcon = (props) => {
    return jsxRuntimeExports.jsx(LoginIcon, { ...props });
};
const CustomGoogleIcon = (props) => {
    return jsxRuntimeExports.jsx(GoogleIcon, { ...props });
};
const CustomGitHubIcon = (props) => {
    return jsxRuntimeExports.jsx(GitHubIcon, { ...props });
};

const {useState} = await importShared('react');
const LoginPage = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value
    }));
    if (error) setError(null);
  };
  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);
    setError(null);
    try {
      await new Promise((resolve) => setTimeout(resolve, 2e3));
      if (formData.email === "<EMAIL>" && formData.password === "password") {
        alert("Login successful! 🎉");
      } else {
        setError("Invalid email or password. Try <EMAIL> / password");
      }
    } catch (err) {
      setError("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  const handleSocialLogin = (provider) => {
    alert(`${provider} login clicked! (Not implemented)`);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(CustomThemeProvider, { theme: appTheme, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomContainer, { maxWidth: "sm", variant: "padded", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      CustomCard,
      {
        variant: "feature",
        title: "Welcome Back",
        subtitle: "Sign in to your account",
        icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLoginIcon, {}),
        headerColor: "#9c27b0",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { component: "form", onSubmit: handleSubmit, sx: { mt: 2 }, children: [
          error && /* @__PURE__ */ jsxRuntimeExports.jsx(CustomAlert, { severity: "error", sx: { mb: 3 }, children: error }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              fullWidth: true,
              label: "Email Address",
              type: "email",
              value: formData.email,
              onChange: handleInputChange("email"),
              required: true,
              sx: { mb: 3 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomEmailIcon, { color: "action" }) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              fullWidth: true,
              label: "Password",
              type: showPassword ? "text" : "password",
              value: formData.password,
              onChange: handleInputChange("password"),
              required: true,
              sx: { mb: 3 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLockIcon, { color: "action" }) }),
                endAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  CustomIconButton,
                  {
                    onClick: () => setShowPassword(!showPassword),
                    edge: "end",
                    children: showPassword ? /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityOffIcon, {}) : /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityIcon, {})
                  }
                ) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomButton,
            {
              type: "submit",
              variant: "gradient",
              fullWidth: true,
              loading,
              icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLoginIcon, {}),
              sx: { mb: 3, py: 1.5 },
              children: "Sign In"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomDivider, { sx: { my: 3 }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "OR" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { display: "flex", gap: 2, mb: 3 }, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomButton,
              {
                variant: "outlined-hover",
                fullWidth: true,
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomGoogleIcon, {}),
                onClick: () => handleSocialLogin("Google"),
                children: "Google"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomButton,
              {
                variant: "outlined-hover",
                fullWidth: true,
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomGitHubIcon, {}),
                onClick: () => handleSocialLogin("GitHub"),
                children: "GitHub"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { textAlign: "center" }, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomTypography, { variant: "body2", color: "text.secondary", children: [
              "Don't have an account?",
              " ",
              /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLink, { href: "#", color: "primary", sx: { textDecoration: "none" }, children: "Sign up here" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", sx: { mt: 1 }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLink, { href: "#", color: "primary", sx: { textDecoration: "none" }, children: "Forgot your password?" }) })
          ] })
        ] })
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomPaper, { variant: "glass", sx: { mt: 3, textAlign: "center" }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "Demo Credentials:" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "Email: <EMAIL>" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "Password: password" })
    ] })
  ] }) });
};

export { LoginPage, appTheme as a, LoginPage as default };
