'use strict';

var styles = require('@mui/material/styles');
var jsxRuntime = require('react/jsx-runtime');
var material = require('@mui/material');
var iconsMaterial = require('@mui/icons-material');

// Custom color palette
const customColors = {
    primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
    },
    secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
    },
    success: {
        main: '#2e7d32',
        light: '#4caf50',
        dark: '#1b5e20',
    },
    warning: {
        main: '#ed6c02',
        light: '#ff9800',
        dark: '#e65100',
    },
    error: {
        main: '#d32f2f',
        light: '#ef5350',
        dark: '#c62828',
    },
};
// Base theme configuration
const baseThemeOptions = {
    palette: {
        ...customColors,
    },
    typography: {
        fontFamily: [
            'Roboto',
            '-apple-system',
            'BlinkMacSystemFont',
            '"Segoe UI"',
            'Arial',
            'sans-serif',
        ].join(','),
    },
    shape: {
        borderRadius: 8,
    },
    spacing: 8,
};
// Component overrides for customized components
const componentOverrides = {
    MuiButton: {
        styleOverrides: {
            root: {
                textTransform: 'none',
                borderRadius: 8,
                fontWeight: 500,
                padding: '8px 16px',
                boxShadow: 'none',
                '&:hover': {
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                },
            },
            contained: {
                '&:hover': {
                    boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                },
            },
        },
    },
    MuiPaper: {
        styleOverrides: {
            root: {
                borderRadius: 12,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
            elevation1: {
                boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
            },
            elevation2: {
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
            elevation3: {
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            },
        },
    },
    MuiCard: {
        styleOverrides: {
            root: {
                borderRadius: 12,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                '&:hover': {
                    boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
                },
            },
        },
    },
};
// Create the theme
const appTheme = styles.createTheme({
    ...baseThemeOptions,
    components: componentOverrides,
});

// Custom styled button variants
const StyledButton = material.styled(material.Button)(({ theme, customVariant }) => ({
    // Base styles
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 500,
    padding: '10px 20px',
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'gradient' && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        color: 'white',
        border: 'none',
        '&:hover': {
            background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
        },
    }),
    ...(customVariant === 'outlined-hover' && {
        border: `2px solid ${theme.palette.primary.main}`,
        color: theme.palette.primary.main,
        backgroundColor: 'transparent',
        '&:hover': {
            backgroundColor: theme.palette.primary.main,
            color: 'white',
            transform: 'scale(1.05)',
        },
    }),
    ...(customVariant === 'soft' && {
        backgroundColor: theme.palette.primary.light + '20', // 20% opacity
        color: theme.palette.primary.main,
        border: 'none',
        '&:hover': {
            backgroundColor: theme.palette.primary.light + '40', // 40% opacity
            transform: 'translateY(-1px)',
        },
    }),
}));
const CustomButton$1 = ({ variant = 'contained', loading = false, icon, children, disabled, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['gradient', 'outlined-hover', 'soft'].includes(variant);
    const muiVariant = isCustomVariant ? 'contained' : variant;
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntime.jsx(StyledButton, { variant: muiVariant, customVariant: customVariant, disabled: disabled || loading, startIcon: loading ? undefined : icon, ...props, children: loading ? (jsxRuntime.jsxs(jsxRuntime.Fragment, { children: [jsxRuntime.jsx("span", { style: { marginRight: 8 }, children: "\u23F3" }), "Loading..."] })) : (children) }));
};

CustomButton;

// Custom styled paper variants
const StyledPaper = material.styled(material.Paper)(({ theme, customVariant }) => ({
    // Base styles
    borderRadius: 12,
    padding: theme.spacing(3),
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'bordered' && {
        border: `2px solid ${theme.palette.primary.main}`,
        boxShadow: 'none',
        '&:hover': {
            borderColor: theme.palette.primary.dark,
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        },
    }),
    ...(customVariant === 'gradient' && {
        background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${theme.palette.primary.light}10)`,
        border: `1px solid ${theme.palette.primary.light}30`,
        '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
        },
    }),
    ...(customVariant === 'glass' && {
        backgroundColor: theme.palette.background.paper + 'CC', // 80% opacity
        backdropFilter: 'blur(10px)',
        border: `1px solid ${theme.palette.divider}`,
        '&:hover': {
            backgroundColor: theme.palette.background.paper + 'DD', // 85% opacity
            transform: 'translateY(-1px)',
        },
    }),
    ...(customVariant === 'elevated' && {
        boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        '&:hover': {
            boxShadow: '0 12px 40px rgba(0,0,0,0.18)',
            transform: 'translateY(-4px)',
        },
    }),
}));
const CustomPaper$1 = ({ variant = 'elevation', padding, hover = true, children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['bordered', 'gradient', 'glass', 'elevated'].includes(variant);
    const muiVariant = isCustomVariant ? 'elevation' : variant;
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntime.jsx(StyledPaper, { variant: muiVariant, customVariant: customVariant, sx: {
            ...(padding && { padding }),
            ...(hover && {
                cursor: 'pointer',
                '&:hover': {
                    transform: 'translateY(-2px)',
                },
            }),
            ...sx,
        }, ...props, children: children }));
};

CustomPaper;

// Custom styled card variants
const StyledCard = material.styled(material.Card)(({ theme, customVariant }) => ({
    // Base styles
    borderRadius: 12,
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    // Custom variant styles
    ...(customVariant === 'interactive' && {
        '&:hover': {
            transform: 'translateY(-8px)',
            boxShadow: '0 12px 32px rgba(0,0,0,0.2)',
        },
    }),
    ...(customVariant === 'feature' && {
        border: `2px solid ${theme.palette.primary.light}`,
        backgroundColor: theme.palette.primary.light + '05',
        '&:hover': {
            borderColor: theme.palette.primary.main,
            backgroundColor: theme.palette.primary.light + '10',
            transform: 'scale(1.02)',
        },
    }),
    ...(customVariant === 'minimal' && {
        boxShadow: 'none',
        border: `1px solid ${theme.palette.divider}`,
        '&:hover': {
            borderColor: theme.palette.primary.main,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
    }),
}));
const CustomCard$1 = ({ variant = 'elevation', title, subtitle, actions, icon, headerColor, children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['interactive', 'feature', 'minimal'].includes(variant);
    const muiVariant = isCustomVariant ? 'elevation' : variant;
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntime.jsxs(StyledCard, { variant: muiVariant, customVariant: customVariant, sx: sx, ...props, children: [(title || subtitle || icon) && (jsxRuntime.jsx(material.Box, { sx: {
                    p: 2,
                    pb: title || subtitle ? 1 : 2,
                    ...(headerColor && {
                        backgroundColor: headerColor,
                        color: 'white',
                        '&:first-of-type': {
                            borderRadius: '12px 12px 0 0',
                        },
                    }),
                }, children: jsxRuntime.jsxs(material.Box, { sx: { display: 'flex', alignItems: 'center', gap: 2 }, children: [icon && (jsxRuntime.jsx(material.Box, { sx: {
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 40,
                                height: 40,
                                borderRadius: 2,
                                backgroundColor: headerColor ? 'rgba(255,255,255,0.2)' : 'primary.light',
                                color: headerColor ? 'white' : 'primary.main',
                            }, children: icon })), jsxRuntime.jsxs(material.Box, { sx: { flexGrow: 1 }, children: [title && (jsxRuntime.jsx(material.Typography, { variant: "h6", component: "h2", gutterBottom: !!subtitle, children: title })), subtitle && (jsxRuntime.jsx(material.Typography, { variant: "body2", color: "text.secondary", children: subtitle }))] })] }) })), jsxRuntime.jsx(material.CardContent, { sx: { pt: (title || subtitle || icon) ? 1 : 2 }, children: children }), actions && (jsxRuntime.jsx(material.CardActions, { sx: { p: 2, pt: 0 }, children: actions }))] }));
};

CustomCard;

// Custom styled box variants
const StyledBox = material.styled(material.Box)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'flex-center' && {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    }),
    ...(customVariant === 'flex-column' && {
        display: 'flex',
        flexDirection: 'column',
    }),
    ...(customVariant === 'flex-between' && {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    }),
    ...(customVariant === 'grid-container' && {
        display: 'grid',
        gap: theme.spacing(2),
    }),
}));
const CustomBox = ({ variant = 'default', children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['flex-center', 'flex-column', 'flex-between', 'grid-container'].includes(variant);
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntime.jsx(StyledBox, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled grid variants
const StyledGrid = material.styled(material.Grid)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'responsive-cards' && {
        '& > .MuiGrid-item': {
            display: 'flex',
            '& > *': {
                width: '100%',
            },
        },
    }),
    ...(customVariant === 'equal-height' && {
        '& > .MuiGrid-item': {
            display: 'flex',
            alignItems: 'stretch',
            '& > *': {
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
            },
        },
    }),
    ...(customVariant === 'masonry' && {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: theme.spacing(3),
        alignItems: 'start',
    }),
}));
const CustomGrid = ({ variant = 'default', children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['responsive-cards', 'equal-height', 'masonry'].includes(variant);
    const customVariant = isCustomVariant ? variant : undefined;
    // For masonry variant, don't use Material-UI Grid
    if (variant === 'masonry') {
        return (jsxRuntime.jsx(StyledGrid, { customVariant: customVariant, sx: sx, ...props, children: children }));
    }
    return (jsxRuntime.jsx(StyledGrid, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled typography variants
const StyledTypography = material.styled(material.Typography)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'gradient-text' && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        fontWeight: 600,
    }),
    ...(customVariant === 'highlight' && {
        backgroundColor: theme.palette.primary.light + '20',
        padding: '2px 8px',
        borderRadius: 4,
        fontWeight: 500,
        color: theme.palette.primary.dark,
    }),
    ...(customVariant === 'code' && {
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        backgroundColor: theme.palette.grey[100],
        padding: '2px 6px',
        borderRadius: 4,
        fontSize: '0.875em',
        border: `1px solid ${theme.palette.grey[300]}`,
    }),
    ...(customVariant === 'caption-bold' && {
        fontWeight: 600,
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        fontSize: '0.75rem',
    }),
}));
const CustomTypography = ({ customVariant, children, sx, ...props }) => {
    return (jsxRuntime.jsx(StyledTypography, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled container variants
const StyledContainer = material.styled(material.Container)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    // Custom variant styles
    ...(customVariant === 'centered' && {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        textAlign: 'center',
    }),
    ...(customVariant === 'full-height' && {
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
    }),
    ...(customVariant === 'padded' && {
        paddingTop: theme.spacing(4),
        paddingBottom: theme.spacing(4),
        [theme.breakpoints.up('md')]: {
            paddingTop: theme.spacing(6),
            paddingBottom: theme.spacing(6),
        },
    }),
    ...(customVariant === 'narrow' && {
        maxWidth: '600px !important',
    }),
}));
const CustomContainer = ({ variant = 'default', children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['centered', 'full-height', 'padded', 'narrow'].includes(variant);
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntime.jsx(StyledContainer, { customVariant: customVariant, sx: sx, ...props, children: children }));
};

// Custom styled app bar variants
const StyledAppBar = material.styled(material.AppBar)(({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    // Custom variant styles
    ...(customVariant === 'gradient' && {
        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
        '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(45deg, rgba(255,255,255,0.1), transparent)',
            pointerEvents: 'none',
        },
    }),
    ...(customVariant === 'glass' && {
        backgroundColor: theme.palette.background.paper + 'CC',
        backdropFilter: 'blur(10px)',
        border: `1px solid ${theme.palette.divider}`,
        color: theme.palette.text.primary,
    }),
    ...(customVariant === 'minimal' && {
        backgroundColor: 'transparent',
        boxShadow: 'none',
        borderBottom: `1px solid ${theme.palette.divider}`,
        color: theme.palette.text.primary,
    }),
    ...(customVariant === 'elevated' && {
        boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
        '&:hover': {
            boxShadow: '0 6px 20px rgba(0,0,0,0.2)',
        },
    }),
}));
const CustomAppBar = ({ variant = 'default', logo, actions, navigation, children, sx, ...props }) => {
    // Handle custom variants
    const isCustomVariant = ['gradient', 'glass', 'minimal', 'elevated'].includes(variant);
    const customVariant = isCustomVariant ? variant : undefined;
    return (jsxRuntime.jsx(StyledAppBar, { customVariant: customVariant, sx: sx, ...props, children: jsxRuntime.jsxs(material.Toolbar, { children: [logo && (jsxRuntime.jsx(material.Box, { sx: { display: 'flex', alignItems: 'center', mr: 2 }, children: logo })), navigation && (jsxRuntime.jsx(material.Box, { sx: { display: 'flex', alignItems: 'center', flexGrow: 1 }, children: navigation })), children && (jsxRuntime.jsx(material.Box, { sx: { display: 'flex', alignItems: 'center', flexGrow: 1 }, children: children })), actions && (jsxRuntime.jsx(material.Box, { sx: { display: 'flex', alignItems: 'center', gap: 1 }, children: actions }))] }) }));
};

const CustomTextField = (props) => {
    return jsxRuntime.jsx(material.TextField, { ...props });
};

const CustomAlert = (props) => {
    return jsxRuntime.jsx(material.Alert, { ...props });
};

const CustomInputAdornment = (props) => {
    return jsxRuntime.jsx(material.InputAdornment, { ...props });
};

const CustomIconButton = (props) => {
    return jsxRuntime.jsx(material.IconButton, { ...props });
};

const CustomLink = (props) => {
    return jsxRuntime.jsx(material.Link, { ...props });
};

const CustomDivider = (props) => {
    return jsxRuntime.jsx(material.Divider, { ...props });
};

const CustomThemeProvider = ({ theme, children }) => {
    return jsxRuntime.jsx(styles.ThemeProvider, { theme: theme, children: children });
};

const CustomVisibilityIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.Visibility, { ...props });
};
const CustomVisibilityOffIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.VisibilityOff, { ...props });
};
const CustomEmailIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.Email, { ...props });
};
const CustomLockIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.Lock, { ...props });
};
const CustomLoginIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.Login, { ...props });
};
const CustomGoogleIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.Google, { ...props });
};
const CustomGitHubIcon = (props) => {
    return jsxRuntime.jsx(iconsMaterial.GitHub, { ...props });
};

exports.CustomAlert = CustomAlert;
exports.CustomAppBar = CustomAppBar;
exports.CustomBox = CustomBox;
exports.CustomButton = CustomButton$1;
exports.CustomCard = CustomCard$1;
exports.CustomContainer = CustomContainer;
exports.CustomDivider = CustomDivider;
exports.CustomEmailIcon = CustomEmailIcon;
exports.CustomGitHubIcon = CustomGitHubIcon;
exports.CustomGoogleIcon = CustomGoogleIcon;
exports.CustomGrid = CustomGrid;
exports.CustomIconButton = CustomIconButton;
exports.CustomInputAdornment = CustomInputAdornment;
exports.CustomLink = CustomLink;
exports.CustomLockIcon = CustomLockIcon;
exports.CustomLoginIcon = CustomLoginIcon;
exports.CustomPaper = CustomPaper$1;
exports.CustomTextField = CustomTextField;
exports.CustomThemeProvider = CustomThemeProvider;
exports.CustomTypography = CustomTypography;
exports.CustomVisibilityIcon = CustomVisibilityIcon;
exports.CustomVisibilityOffIcon = CustomVisibilityOffIcon;
exports.appTheme = appTheme;
//# sourceMappingURL=index.js.map
