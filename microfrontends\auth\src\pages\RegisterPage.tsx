import React, { useState } from 'react'
import {
  CustomContainer,
  CustomPaper,
  CustomBox,
  CustomTypography,
  CustomTextField,
  CustomButton,
  CustomAlert,
  CustomDivider,
  CustomLink,
  CustomInputAdornment,
  CustomIconButton,
  CustomEmailIcon,
  CustomLockIcon,
  CustomPersonIcon,
  CustomVisibilityIcon,
  CustomVisibilityOffIcon,
  CustomGoogleIcon,
  CustomGitHubIcon,
} from '@app-front/ui-components'

interface RegisterFormData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
}

interface RegisterFormErrors {
  firstName?: string
  lastName?: string
  email?: string
  password?: string
  confirmPassword?: string
  general?: string
}

export const RegisterPage: React.FC = () => {
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
  })

  const [errors, setErrors] = useState<RegisterFormErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: keyof RegisterFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }))
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: RegisterFormErrors = {}

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setErrors({})

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // TODO: Implement actual registration logic
      console.log('Registration data:', {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
      })
      
      // For now, show success message
      setErrors({ general: 'Registration successful! Please check your email to verify your account.' })
      
    } catch (error) {
      setErrors({
        general: 'Registration failed. Please try again later.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSocialRegister = (provider: 'google' | 'github') => {
    console.log(`Register with ${provider}`)
    // TODO: Implement social registration
  }

  return (
    <CustomContainer maxWidth="sm">
      <CustomBox
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4,
        }}
      >
        <CustomPaper variant="elevated" sx={{ p: 4, width: '100%' }}>
          <CustomBox sx={{ textAlign: 'center', mb: 4 }}>
            <CustomTypography variant="h4" component="h1" gutterBottom>
              Create Account
            </CustomTypography>
            <CustomTypography variant="body1" color="text.secondary">
              Join us to get started with your journey
            </CustomTypography>
          </CustomBox>

          {errors.general && (
            <CustomAlert 
              severity={errors.general.includes('successful') ? 'success' : 'error'} 
              sx={{ mb: 3 }}
            >
              {errors.general}
            </CustomAlert>
          )}

          <CustomBox component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <CustomBox sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <CustomTextField
                required
                fullWidth
                id="firstName"
                label="First Name"
                name="firstName"
                autoComplete="given-name"
                value={formData.firstName}
                onChange={handleInputChange('firstName')}
                error={!!errors.firstName}
                helperText={errors.firstName}
                InputProps={{
                  startAdornment: (
                    <CustomInputAdornment position="start">
                      <CustomPersonIcon />
                    </CustomInputAdornment>
                  ),
                }}
              />
              <CustomTextField
                required
                fullWidth
                id="lastName"
                label="Last Name"
                name="lastName"
                autoComplete="family-name"
                value={formData.lastName}
                onChange={handleInputChange('lastName')}
                error={!!errors.lastName}
                helperText={errors.lastName}
                InputProps={{
                  startAdornment: (
                    <CustomInputAdornment position="start">
                      <CustomPersonIcon />
                    </CustomInputAdornment>
                  ),
                }}
              />
            </CustomBox>

            <CustomTextField
              required
              fullWidth
              id="email"
              label="Email Address"
              name="email"
              autoComplete="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              error={!!errors.email}
              helperText={errors.email}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: (
                  <CustomInputAdornment position="start">
                    <CustomEmailIcon />
                  </CustomInputAdornment>
                ),
              }}
            />

            <CustomTextField
              required
              fullWidth
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleInputChange('password')}
              error={!!errors.password}
              helperText={errors.password}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: (
                  <CustomInputAdornment position="start">
                    <CustomLockIcon />
                  </CustomInputAdornment>
                ),
                endAdornment: (
                  <CustomInputAdornment position="end">
                    <CustomIconButton
                      aria-label="toggle password visibility"
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <CustomVisibilityOffIcon /> : <CustomVisibilityIcon />}
                    </CustomIconButton>
                  </CustomInputAdornment>
                ),
              }}
            />

            <CustomTextField
              required
              fullWidth
              name="confirmPassword"
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              autoComplete="new-password"
              value={formData.confirmPassword}
              onChange={handleInputChange('confirmPassword')}
              error={!!errors.confirmPassword}
              helperText={errors.confirmPassword}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <CustomInputAdornment position="start">
                    <CustomLockIcon />
                  </CustomInputAdornment>
                ),
                endAdornment: (
                  <CustomInputAdornment position="end">
                    <CustomIconButton
                      aria-label="toggle confirm password visibility"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      edge="end"
                    >
                      {showConfirmPassword ? <CustomVisibilityOffIcon /> : <CustomVisibilityIcon />}
                    </CustomIconButton>
                  </CustomInputAdornment>
                ),
              }}
            />

            <CustomButton
              type="submit"
              fullWidth
              variant="gradient"
              disabled={isLoading}
              sx={{ mb: 2 }}
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </CustomButton>

            <CustomDivider sx={{ my: 3 }}>
              <CustomTypography variant="body2" color="text.secondary">
                Or register with
              </CustomTypography>
            </CustomDivider>

            <CustomBox sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <CustomButton
                fullWidth
                variant="outlined-hover"
                startIcon={<CustomGoogleIcon />}
                onClick={() => handleSocialRegister('google')}
                disabled={isLoading}
              >
                Google
              </CustomButton>
              <CustomButton
                fullWidth
                variant="outlined-hover"
                startIcon={<CustomGitHubIcon />}
                onClick={() => handleSocialRegister('github')}
                disabled={isLoading}
              >
                GitHub
              </CustomButton>
            </CustomBox>

            <CustomBox sx={{ textAlign: 'center' }}>
              <CustomTypography variant="body2" color="text.secondary">
                Already have an account?{' '}
                <CustomLink href="/auth/login" color="primary" sx={{ textDecoration: 'none', fontWeight: 'bold' }}>
                  Sign in here
                </CustomLink>
              </CustomTypography>
            </CustomBox>
          </CustomBox>
        </CustomPaper>
      </CustomBox>
    </CustomContainer>
  )
}

export default RegisterPage
