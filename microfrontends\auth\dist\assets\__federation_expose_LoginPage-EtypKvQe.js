import { importShared } from './__federation_fn_import-DL9kAWwu.js';
import { j as jsxRuntimeExports } from './createSvgIcon-BvEkhiOn.js';
import { C as CustomThemeProvider, a as appTheme, b as CustomContainer, c as CustomCard, d as CustomBox, e as CustomAlert, f as CustomTextField, g as CustomInputAdornment, h as CustomEmailIcon, i as CustomIconButton, j as CustomVisibilityOffIcon, k as CustomVisibilityIcon, l as CustomLockIcon, m as CustomButton, n as CustomLoginIcon, o as CustomDivider, p as CustomTypography, q as CustomGoogleIcon, r as CustomGitHubIcon, s as CustomLink, t as CustomPaper } from './index.esm-Cpksrx5x.js';

const {useState} = await importShared('react');
const LoginPage = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value
    }));
    if (error) setError(null);
  };
  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);
    setError(null);
    try {
      await new Promise((resolve) => setTimeout(resolve, 2e3));
      if (formData.email === "<EMAIL>" && formData.password === "password") {
        alert("Login successful! 🎉");
      } else {
        setError("Invalid email or password. Try <EMAIL> / password");
      }
    } catch (err) {
      setError("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  const handleSocialLogin = (provider) => {
    alert(`${provider} login clicked! (Not implemented)`);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(CustomThemeProvider, { theme: appTheme, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomContainer, { maxWidth: "sm", variant: "padded", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      CustomCard,
      {
        variant: "feature",
        title: "Welcome Back",
        subtitle: "Sign in to your account",
        icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLoginIcon, {}),
        headerColor: "#9c27b0",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { component: "form", onSubmit: handleSubmit, sx: { mt: 2 }, children: [
          error && /* @__PURE__ */ jsxRuntimeExports.jsx(CustomAlert, { severity: "error", sx: { mb: 3 }, children: error }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              fullWidth: true,
              label: "Email Address",
              type: "email",
              value: formData.email,
              onChange: handleInputChange("email"),
              required: true,
              sx: { mb: 3 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomEmailIcon, { color: "action" }) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              fullWidth: true,
              label: "Password",
              type: showPassword ? "text" : "password",
              value: formData.password,
              onChange: handleInputChange("password"),
              required: true,
              sx: { mb: 3 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLockIcon, { color: "action" }) }),
                endAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  CustomIconButton,
                  {
                    onClick: () => setShowPassword(!showPassword),
                    edge: "end",
                    children: showPassword ? /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityOffIcon, {}) : /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityIcon, {})
                  }
                ) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomButton,
            {
              type: "submit",
              variant: "gradient",
              fullWidth: true,
              loading,
              icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLoginIcon, {}),
              sx: { mb: 3, py: 1.5 },
              children: "Sign In"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomDivider, { sx: { my: 3 }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "OR" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { display: "flex", gap: 2, mb: 3 }, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomButton,
              {
                variant: "outlined-hover",
                fullWidth: true,
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomGoogleIcon, {}),
                onClick: () => handleSocialLogin("Google"),
                children: "Google"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomButton,
              {
                variant: "outlined-hover",
                fullWidth: true,
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomGitHubIcon, {}),
                onClick: () => handleSocialLogin("GitHub"),
                children: "GitHub"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { textAlign: "center" }, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomTypography, { variant: "body2", color: "text.secondary", children: [
              "Don't have an account?",
              " ",
              /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLink, { href: "/auth/register", color: "primary", sx: { textDecoration: "none", fontWeight: "bold" }, children: "Sign up here" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", sx: { mt: 1 }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLink, { href: "#", color: "primary", sx: { textDecoration: "none" }, children: "Forgot your password?" }) })
          ] })
        ] })
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomPaper, { variant: "glass", sx: { mt: 3, textAlign: "center" }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "Demo Credentials:" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "Email: <EMAIL>" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "Password: password" })
    ] })
  ] }) });
};

export { LoginPage, LoginPage as default };
