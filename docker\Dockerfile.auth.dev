# Development Dockerfile for Auth Microfrontend
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for hot reload
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./
COPY microfrontends/auth/package*.json ./microfrontends/auth/
COPY packages/ui-components/package*.json ./packages/ui-components/

# Install dependencies
RUN npm install
RUN cd microfrontends/auth && npm install
RUN cd packages/ui-components && npm install

# Copy source code
COPY . .

# Build ui-components first
RUN cd packages/ui-components && npm run build

# Expose port
EXPOSE 3001

# Set environment for development
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true

# Start development server
CMD ["npm", "run", "dev:auth"]
