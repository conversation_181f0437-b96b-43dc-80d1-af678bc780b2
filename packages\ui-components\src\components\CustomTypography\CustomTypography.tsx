import React from 'react'
import { Typography, TypographyProps, styled } from '@mui/material'

// Custom styled typography variants
const StyledTypography = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'customVariant',
})<{ customVariant?: 'gradient-text' | 'highlight' | 'code' | 'caption-bold' }>(
  ({ theme, customVariant }) => ({
    // Base styles
    transition: 'all 0.3s ease',

    // Custom variant styles
    ...(customVariant === 'gradient-text' && {
      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      fontWeight: 600,
    }),

    ...(customVariant === 'highlight' && {
      backgroundColor: theme.palette.primary.light + '20',
      padding: '2px 8px',
      borderRadius: 4,
      fontWeight: 500,
      color: theme.palette.primary.dark,
    }),

    ...(customVariant === 'code' && {
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      backgroundColor: theme.palette.grey[100],
      padding: '2px 6px',
      borderRadius: 4,
      fontSize: '0.875em',
      border: `1px solid ${theme.palette.grey[300]}`,
    }),

    ...(customVariant === 'caption-bold' && {
      fontWeight: 600,
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
      fontSize: '0.75rem',
    }),
  })
)

// Custom Typography component interface
export interface CustomTypographyProps extends TypographyProps {
  customVariant?: 'gradient-text' | 'highlight' | 'code' | 'caption-bold'
}

export const CustomTypography: React.FC<CustomTypographyProps> = ({
  customVariant,
  children,
  sx,
  ...props
}) => {
  return (
    <StyledTypography
      customVariant={customVariant}
      sx={sx}
      {...props}
    >
      {children}
    </StyledTypography>
  )
}

export default CustomTypography
