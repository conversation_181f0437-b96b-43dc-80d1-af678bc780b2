import { importShared } from './__federation_fn_import-DL9kAWwu.js';
import { j as jsxRuntimeExports } from './createSvgIcon-BvEkhiOn.js';
import { b as CustomContainer, d as CustomBox, t as CustomPaper, p as CustomTypography, e as CustomAlert, f as CustomTextField, g as CustomInputAdornment, u as CustomPersonIcon, h as CustomEmailIcon, i as CustomIconButton, j as CustomVisibilityOffIcon, k as CustomVisibilityIcon, l as CustomLockIcon, m as CustomButton, o as CustomDivider, q as CustomGoogleIcon, r as CustomGitHubIcon, s as CustomLink } from './index.esm-Cpksrx5x.js';

const {useState} = await importShared('react');
const RegisterPage = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: ""
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value
    }));
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: void 0
      }));
    }
  };
  const validateForm = () => {
    const newErrors = {};
    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long";
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = "Password must contain at least one uppercase letter, one lowercase letter, and one number";
    }
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!validateForm()) {
      return;
    }
    setIsLoading(true);
    setErrors({});
    try {
      await new Promise((resolve) => setTimeout(resolve, 2e3));
      console.log("Registration data:", {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password
      });
      setErrors({ general: "Registration successful! Please check your email to verify your account." });
    } catch (error) {
      setErrors({
        general: "Registration failed. Please try again later."
      });
    } finally {
      setIsLoading(false);
    }
  };
  const handleSocialRegister = (provider) => {
    console.log(`Register with ${provider}`);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(CustomContainer, { maxWidth: "sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
    CustomBox,
    {
      sx: {
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        py: 4
      },
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomPaper, { variant: "elevated", sx: { p: 4, width: "100%" }, children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { textAlign: "center", mb: 4 }, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "h4", component: "h1", gutterBottom: true, children: "Create Account" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body1", color: "text.secondary", children: "Join us to get started with your journey" })
        ] }),
        errors.general && /* @__PURE__ */ jsxRuntimeExports.jsx(
          CustomAlert,
          {
            severity: errors.general.includes("successful") ? "success" : "error",
            sx: { mb: 3 },
            children: errors.general
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { component: "form", onSubmit: handleSubmit, sx: { mt: 1 }, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { display: "flex", gap: 2, mb: 2 }, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomTextField,
              {
                required: true,
                fullWidth: true,
                id: "firstName",
                label: "First Name",
                name: "firstName",
                autoComplete: "given-name",
                value: formData.firstName,
                onChange: handleInputChange("firstName"),
                error: !!errors.firstName,
                helperText: errors.firstName,
                InputProps: {
                  startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomPersonIcon, {}) })
                }
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomTextField,
              {
                required: true,
                fullWidth: true,
                id: "lastName",
                label: "Last Name",
                name: "lastName",
                autoComplete: "family-name",
                value: formData.lastName,
                onChange: handleInputChange("lastName"),
                error: !!errors.lastName,
                helperText: errors.lastName,
                InputProps: {
                  startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomPersonIcon, {}) })
                }
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              required: true,
              fullWidth: true,
              id: "email",
              label: "Email Address",
              name: "email",
              autoComplete: "email",
              value: formData.email,
              onChange: handleInputChange("email"),
              error: !!errors.email,
              helperText: errors.email,
              sx: { mb: 2 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomEmailIcon, {}) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              required: true,
              fullWidth: true,
              name: "password",
              label: "Password",
              type: showPassword ? "text" : "password",
              id: "password",
              autoComplete: "new-password",
              value: formData.password,
              onChange: handleInputChange("password"),
              error: !!errors.password,
              helperText: errors.password,
              sx: { mb: 2 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLockIcon, {}) }),
                endAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  CustomIconButton,
                  {
                    "aria-label": "toggle password visibility",
                    onClick: () => setShowPassword(!showPassword),
                    edge: "end",
                    children: showPassword ? /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityOffIcon, {}) : /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityIcon, {})
                  }
                ) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomTextField,
            {
              required: true,
              fullWidth: true,
              name: "confirmPassword",
              label: "Confirm Password",
              type: showConfirmPassword ? "text" : "password",
              id: "confirmPassword",
              autoComplete: "new-password",
              value: formData.confirmPassword,
              onChange: handleInputChange("confirmPassword"),
              error: !!errors.confirmPassword,
              helperText: errors.confirmPassword,
              sx: { mb: 3 },
              InputProps: {
                startAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "start", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLockIcon, {}) }),
                endAdornment: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomInputAdornment, { position: "end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  CustomIconButton,
                  {
                    "aria-label": "toggle confirm password visibility",
                    onClick: () => setShowConfirmPassword(!showConfirmPassword),
                    edge: "end",
                    children: showConfirmPassword ? /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityOffIcon, {}) : /* @__PURE__ */ jsxRuntimeExports.jsx(CustomVisibilityIcon, {})
                  }
                ) })
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            CustomButton,
            {
              type: "submit",
              fullWidth: true,
              variant: "gradient",
              disabled: isLoading,
              sx: { mb: 2 },
              children: isLoading ? "Creating Account..." : "Create Account"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomDivider, { sx: { my: 3 }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTypography, { variant: "body2", color: "text.secondary", children: "Or register with" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomBox, { sx: { display: "flex", gap: 2, mb: 3 }, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomButton,
              {
                fullWidth: true,
                variant: "outlined-hover",
                startIcon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomGoogleIcon, {}),
                onClick: () => handleSocialRegister("google"),
                disabled: isLoading,
                children: "Google"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CustomButton,
              {
                fullWidth: true,
                variant: "outlined-hover",
                startIcon: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomGitHubIcon, {}),
                onClick: () => handleSocialRegister("github"),
                disabled: isLoading,
                children: "GitHub"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomBox, { sx: { textAlign: "center" }, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CustomTypography, { variant: "body2", color: "text.secondary", children: [
            "Already have an account?",
            " ",
            /* @__PURE__ */ jsxRuntimeExports.jsx(CustomLink, { href: "/auth/login", color: "primary", sx: { textDecoration: "none", fontWeight: "bold" }, children: "Sign in here" })
          ] }) })
        ] })
      ] })
    }
  ) });
};

export { RegisterPage, RegisterPage as default };
