import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { appTheme } from '@app-front/ui-components'
import LoginPage from './pages/LoginPage'

function App() {
  return (
    <ThemeProvider theme={appTheme}>
      <CssBaseline />
      <Router>
        <Routes>
          <Route path="/" element={<LoginPage />} />
          <Route path="/login" element={<LoginPage />} />
          {/* Add more auth routes here like register, forgot-password, etc. */}
        </Routes>
      </Router>
    </ThemeProvider>
  )
}

export default App
