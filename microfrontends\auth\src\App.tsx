import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { appTheme } from '@app-front/ui-components'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'

function App() {
  // Determine if we're running standalone or as a microfrontend
  const isStandalone = window.location.pathname.startsWith('/auth') === false
  const basePath = isStandalone ? '' : '/auth'

  return (
    <ThemeProvider theme={appTheme}>
      <CssBaseline />
      <Router basename={basePath}>
        <Routes>
          <Route path="/" element={<LoginPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          {/* Add more auth routes here like forgot-password, etc. */}
        </Routes>
      </Router>
    </ThemeProvider>
  )
}

export default App
