import React from 'react';
import { AppBarProps } from '@mui/material';
export interface CustomAppBarProps extends Omit<AppBarProps, 'variant'> {
    variant?: 'default' | 'gradient' | 'glass' | 'minimal' | 'elevated';
    logo?: React.ReactNode;
    actions?: React.ReactNode;
    navigation?: React.ReactNode;
}
export declare const CustomAppBar: React.FC<CustomAppBarProps>;
export default CustomAppBar;
