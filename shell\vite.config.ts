import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import federation from '@originjs/vite-plugin-federation'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'shell',
      remotes: {
        // Micro frontends will be configured here
        auth: 'http://localhost:3002/assets/remoteEntry.js',
        // dashboard: 'http://localhost:3003/assets/remoteEntry.js',
        // profile: 'http://localhost:3004/assets/remoteEntry.js',
      },
      shared: ['react', 'react-dom', 'react-router-dom', '@mui/material', '@emotion/react', '@emotion/styled'],
    }),
  ],
  optimizeDeps: {
    include: [
      '@mui/material',
      '@mui/icons-material',
      '@emotion/react',
      '@emotion/styled',
      '@mui/material/styles',
      '@mui/styled-engine',
    ],
    force: true,
  },
  define: {
    global: 'globalThis',
  },
  resolve: {
    alias: {
      '@emotion/styled': path.resolve(__dirname, '../node_modules/@emotion/styled'),
      '@emotion/react': path.resolve(__dirname, '../node_modules/@emotion/react'),
    },
  },
  server: {
    port: 3000,
    host: '0.0.0.0', // Allow external connections (required for Docker)
    cors: true,
    watch: {
      usePolling: true, // Required for Docker on Windows/WSL
      interval: 1000,
    },
    hmr: {
      port: 3000, // Hot Module Replacement port
    },
  },
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
  },
  preview: {
    port: 3000,
  },
})
