{"name": "@app-front/ui-components", "version": "1.0.0", "type": "module", "description": "Shared UI Components Library with Material-UI", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "dev": "rollup -c -w", "type-check": "tsc --noEmit"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@mui/material": "^6.3.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "~5.8.3", "rollup": "^4.0.0", "@rollup/plugin-typescript": "^12.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-commonjs": "^26.0.0", "rollup-plugin-peer-deps-external": "^2.2.4"}}