{"name": "@app-front/root", "private": true, "version": "1.0.0", "description": "Micro Frontend Architecture with Module Federation", "scripts": {"dev": "concurrently \"npm run dev:shell\" \"npm run dev:auth\"", "dev:shell": "cd shell && npm run dev", "dev:auth": "cd microfrontends/auth && npm run dev", "build": "npm run build:packages && npm run build:shell && npm run build:auth", "build:packages": "cd packages/ui-components && npm run build", "build:shell": "cd shell && npm run build", "build:auth": "cd microfrontends/auth && npm run build", "docker:build": "cd docker && docker-compose build", "docker:up": "cd docker && docker-compose up -d", "docker:down": "cd docker && docker-compose down", "docker:logs": "cd docker && docker-compose logs -f", "docker:dev": "cd docker && docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "cd docker && docker-compose -f docker-compose.dev.yml down", "docker:dev:logs": "cd docker && docker-compose -f docker-compose.dev.yml logs -f", "docker:clean": "cd docker && docker-compose down -v --rmi all --remove-orphans", "docker:rebuild": "npm run docker:clean && npm run docker:build && npm run docker:up", "install:all": "npm install && cd packages/ui-components && npm install && cd ../../shell && npm install && cd ../microfrontends/auth && npm install", "clean": "rm -rf node_modules packages/*/node_modules shell/node_modules microfrontends/*/node_modules", "lint": "npm run lint:shell && npm run lint:auth", "lint:shell": "cd shell && npm run lint", "lint:auth": "cd microfrontends/auth && npm run lint", "type-check": "npm run type-check:shell && npm run type-check:auth", "type-check:shell": "cd shell && npm run type-check", "type-check:auth": "cd microfrontends/auth && npm run type-check"}, "workspaces": ["packages/*", "shell", "microfrontends/*"], "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/app-front.git"}, "keywords": ["micro-frontend", "module-federation", "react", "vite", "typescript"], "author": "Your Organization", "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1"}}