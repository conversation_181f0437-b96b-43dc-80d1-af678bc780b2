import React from 'react'
import { Link } from 'react-router-dom'
import {
  CustomCard,
  CustomTypography,
  CustomBox,
  CustomButton,
} from '@app-front/ui-components'

interface ModuleCardProps {
  title: string
  description: string
  path: string
  icon: React.ReactNode
  status: 'active' | 'placeholder'
  port: number
}

export const ModuleCard: React.FC<ModuleCardProps> = ({
  title,
  description,
  path,
  icon,
  status,
  port,
}) => {
  return (
    <CustomCard variant="interactive">
      <CustomBox sx={{ p: 3, textAlign: 'center' }}>
        <CustomBox sx={{ mb: 2, color: status === 'active' ? 'primary.main' : 'text.secondary' }}>
          {icon}
        </CustomBox>
        
        <CustomTypography variant="h6" component="h3" gutterBottom>
          {title}
        </CustomTypography>
        
        <CustomTypography variant="body2" color="text.secondary" paragraph>
          {description}
        </CustomTypography>
        
        <CustomTypography variant="caption" color="text.secondary" sx={{ fontFamily: 'monospace', mb: 2, display: 'block' }}>
          Port: {port}
        </CustomTypography>
        
        {status === 'active' ? (
          <CustomButton
            component={Link}
            to={path}
            variant="gradient"
            fullWidth
          >
            Open Module
          </CustomButton>
        ) : (
          <CustomButton
            variant="outlined-hover"
            fullWidth
            disabled
          >
            Coming Soon
          </CustomButton>
        )}
      </CustomBox>
    </CustomCard>
  )
}

export default ModuleCard
