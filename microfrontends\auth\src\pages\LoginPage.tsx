import React, { useState } from 'react'
import {
  CustomButton,
  CustomPaper,
  CustomCard,
  CustomBox,
  CustomTypography,
  CustomContainer,
  CustomTextField,
  CustomAlert,
  CustomInputAdornment,
  CustomIconButton,
  CustomLink,
  CustomDivider,
  CustomThemeProvider,
  CustomVisibilityIcon,
  CustomVisibilityOffIcon,
  CustomEmailIcon,
  CustomLockIcon,
  CustomLoginIcon,
  CustomGoogleIcon,
  CustomGitHubIcon,
  appTheme
} from '@app-front/ui-components'

interface LoginFormData {
  email: string
  password: string
}

export const LoginPage: React.FC = () => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (field: keyof LoginFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock validation
      if (formData.email === '<EMAIL>' && formData.password === 'password') {
        alert('Login successful! 🎉')
        // Here you would typically redirect or update auth state
      } else {
        setError('Invalid email or password. Try <EMAIL> / password')
      }
    } catch (err) {
      setError('Login failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSocialLogin = (provider: string) => {
    alert(`${provider} login clicked! (Not implemented)`)
  }

  return (
    <CustomThemeProvider theme={appTheme}>
      <CustomContainer maxWidth="sm" variant="padded">
        <CustomCard
          variant="feature"
          title="Welcome Back"
          subtitle="Sign in to your account"
          icon={<CustomLoginIcon />}
          headerColor="#9c27b0"
        >
          <CustomBox component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
            {/* Error Alert */}
            {error && (
              <CustomAlert severity="error" sx={{ mb: 3 }}>
                {error}
              </CustomAlert>
            )}

            {/* Email Field */}
            <CustomTextField
              fullWidth
              label="Email Address"
              type="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              required
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <CustomInputAdornment position="start">
                    <CustomEmailIcon color="action" />
                  </CustomInputAdornment>
                ),
              }}
            />

            {/* Password Field */}
            <CustomTextField
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              required
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <CustomInputAdornment position="start">
                    <CustomLockIcon color="action" />
                  </CustomInputAdornment>
                ),
                endAdornment: (
                  <CustomInputAdornment position="end">
                    <CustomIconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <CustomVisibilityOffIcon /> : <CustomVisibilityIcon />}
                    </CustomIconButton>
                  </CustomInputAdornment>
                ),
              }}
            />

            {/* Login Button */}
            <CustomButton
              type="submit"
              variant="gradient"
              fullWidth
              loading={loading}
              icon={<CustomLoginIcon />}
              sx={{ mb: 3, py: 1.5 }}
            >
              Sign In
            </CustomButton>

            {/* Divider */}
            <CustomDivider sx={{ my: 3 }}>
              <CustomTypography variant="body2" color="text.secondary">
                OR
              </CustomTypography>
            </CustomDivider>

            {/* Social Login Buttons */}
            <CustomBox sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <CustomButton
                variant="outlined-hover"
                fullWidth
                icon={<CustomGoogleIcon />}
                onClick={() => handleSocialLogin('Google')}
              >
                Google
              </CustomButton>
              <CustomButton
                variant="outlined-hover"
                fullWidth
                icon={<CustomGitHubIcon />}
                onClick={() => handleSocialLogin('GitHub')}
              >
                GitHub
              </CustomButton>
            </CustomBox>

            {/* Footer Links */}
            <CustomBox sx={{ textAlign: 'center' }}>
              <CustomTypography variant="body2" color="text.secondary">
                Don't have an account?{' '}
                <CustomLink href="/auth/register" color="primary" sx={{ textDecoration: 'none', fontWeight: 'bold' }}>
                  Sign up here
                </CustomLink>
              </CustomTypography>
              <CustomTypography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                <CustomLink href="#" color="primary" sx={{ textDecoration: 'none' }}>
                  Forgot your password?
                </CustomLink>
              </CustomTypography>
            </CustomBox>
          </CustomBox>
        </CustomCard>

        {/* Demo Credentials */}
        <CustomPaper variant="glass" sx={{ mt: 3, textAlign: 'center' }}>
          <CustomTypography variant="body2" color="text.secondary" gutterBottom>
            <strong>Demo Credentials:</strong>
          </CustomTypography>
          <CustomTypography variant="body2" color="text.secondary">
            Email: <EMAIL>
          </CustomTypography>
          <CustomTypography variant="body2" color="text.secondary">
            Password: password
          </CustomTypography>
        </CustomPaper>
      </CustomContainer>
    </CustomThemeProvider>
  )
}

export default LoginPage
