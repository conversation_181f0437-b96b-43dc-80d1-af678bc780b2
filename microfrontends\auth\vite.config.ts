import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import federation from '@originjs/vite-plugin-federation'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'auth',
      filename: 'remoteEntry.js',
      exposes: {
        './AuthApp': './src/App.tsx',
        './LoginPage': './src/pages/LoginPage.tsx',
        './RegisterPage': './src/pages/RegisterPage.tsx',
      },
      shared: ['react', 'react-dom', '@mui/material', '@emotion/react', '@emotion/styled'],
    }),
  ],
  server: {
    port: 3001,
    host: '0.0.0.0', // Allow external connections (required for Docker)
    cors: true,
    watch: {
      usePolling: true, // Required for Docker on Windows/WSL
      interval: 1000,
    },
    hmr: {
      port: 3001, // Hot Module Replacement port
    },
  },
  preview: {
    port: 3001,
    cors: true,
  },
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      external: ['react', 'react-dom'],
    },
  },
})
