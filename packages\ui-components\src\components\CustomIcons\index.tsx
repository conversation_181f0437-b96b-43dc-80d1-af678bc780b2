import React from 'react'
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Login as LoginIcon,
  Google as GoogleIcon,
  GitHub as GitHubIcon,
  Security,
  Dashboard,
  Person,
} from '@mui/icons-material'
import { SvgIconProps } from '@mui/material'

export interface CustomIconProps extends SvgIconProps {
  // Add any custom props here if needed
}

export const CustomVisibilityIcon: React.FC<CustomIconProps> = (props) => {
  return <Visibility {...props} />
}

export const CustomVisibilityOffIcon: React.FC<CustomIconProps> = (props) => {
  return <VisibilityOff {...props} />
}

export const CustomEmailIcon: React.FC<CustomIconProps> = (props) => {
  return <Email {...props} />
}

export const CustomLockIcon: React.FC<CustomIconProps> = (props) => {
  return <Lock {...props} />
}

export const CustomLoginIcon: React.FC<CustomIconProps> = (props) => {
  return <LoginIcon {...props} />
}

export const CustomGoogleIcon: React.FC<CustomIconProps> = (props) => {
  return <GoogleIcon {...props} />
}

export const CustomGitHubIcon: React.FC<CustomIconProps> = (props) => {
  return <GitHubIcon {...props} />
}

export const CustomSecurityIcon: React.FC<CustomIconProps> = (props) => {
  return <Security {...props} />
}

export const CustomDashboardIcon: React.FC<CustomIconProps> = (props) => {
  return <Dashboard {...props} />
}

export const CustomPersonIcon: React.FC<CustomIconProps> = (props) => {
  return <Person {...props} />
}

// Default exports
export { CustomVisibilityIcon as default } from './index'
