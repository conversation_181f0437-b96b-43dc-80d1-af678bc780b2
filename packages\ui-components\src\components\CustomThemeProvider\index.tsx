import React from 'react'
import { ThemeProvider } from '@mui/material/styles'
import { Theme } from '@mui/material/styles'

export interface CustomThemeProviderProps {
  theme: Theme
  children: React.ReactNode
}

export const CustomThemeProvider: React.FC<CustomThemeProviderProps> = ({ theme, children }) => {
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>
}

export default CustomThemeProvider
