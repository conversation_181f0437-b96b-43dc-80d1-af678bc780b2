# UI Components Watcher for Development
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for file watching
RUN apk add --no-cache git

# Copy package files
COPY packages/ui-components/package*.json ./packages/ui-components/

# Install dependencies
RUN cd packages/ui-components && npm install

# Copy source code
COPY packages/ui-components ./packages/ui-components

# Set environment for development
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true

# Watch and rebuild ui-components on changes
WORKDIR /app/packages/ui-components
CMD ["npm", "run", "build:watch"]
