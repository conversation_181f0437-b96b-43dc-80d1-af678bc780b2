{"name": "@app-front/shell", "private": true, "version": "1.0.0", "type": "module", "description": "Micro Frontend Shell Application - Main container for Module Federation", "scripts": {"dev": "vite --port 3000", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview --port 3000", "type-check": "tsc --noEmit"}, "dependencies": {"react-router-dom": "^6.28.1", "@app-front/ui-components": "file:../packages/ui-components"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@mui/material": "^6.3.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@originjs/vite-plugin-federation": "^1.3.6", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}