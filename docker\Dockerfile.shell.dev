# Development Dockerfile for Shell Application
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for hot reload
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./
COPY shell/package*.json ./shell/
COPY packages/ui-components/package*.json ./packages/ui-components/

# Install dependencies
RUN npm install
RUN cd shell && npm install
RUN cd packages/ui-components && npm install

# Copy source code
COPY . .

# Build ui-components first
RUN cd packages/ui-components && npm run build

# Expose port
EXPOSE 3000

# Set environment for development
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true

# Start development server
CMD ["npm", "run", "dev:shell"]
