# Multi-stage Dockerfile for Micro Frontend Architecture
# This builds shared packages and all micro frontends

# ================================
# Stage 1: Base Node.js Image
# ================================
FROM node:20-alpine AS base
WORKDIR /app

# Copy package files for dependency resolution
COPY package*.json ./
COPY packages/ui-components/package.json ./packages/ui-components/
COPY shell/package.json ./shell/
COPY microfrontends/auth/package.json ./microfrontends/auth/

# ================================
# Stage 2: Dependencies Installation
# ================================
FROM base AS deps
# Install all dependencies
RUN npm install

# ================================
# Stage 3: Build Shared Packages
# ================================
FROM deps AS packages-builder
# Copy shared packages source code
COPY packages/ ./packages/

# Build shared packages
WORKDIR /app/packages/ui-components
RUN npm run build

# ================================
# Stage 4: Shell Application
# ================================
FROM packages-builder AS shell-builder
WORKDIR /app

# Copy shell source code
COPY shell/ ./shell/

# Build shell application
WORKDIR /app/shell
RUN npm run build

# ================================
# Stage 5: Auth Micro Frontend
# ================================
FROM packages-builder AS auth-builder
WORKDIR /app

# Copy auth micro frontend source code
COPY microfrontends/auth/ ./microfrontends/auth/

# Build auth micro frontend
WORKDIR /app/microfrontends/auth
RUN npm run build

# ================================
# Stage 6: Production Shell Image
# ================================
FROM nginx:alpine AS shell-production
COPY --from=shell-builder /app/shell/dist /usr/share/nginx/html
COPY docker/nginx/shell.conf /etc/nginx/conf.d/default.conf
EXPOSE 3000
CMD ["nginx", "-g", "daemon off;"]

# ================================
# Stage 7: Production Auth Image
# ================================
FROM nginx:alpine AS auth-production
COPY --from=auth-builder /app/microfrontends/auth/dist /usr/share/nginx/html
COPY docker/nginx/auth.conf /etc/nginx/conf.d/default.conf
EXPOSE 3001
CMD ["nginx", "-g", "daemon off;"]

# ================================
# Stage 8: Development Image
# ================================
FROM packages-builder AS development
WORKDIR /app

# Copy all source code
COPY . .

# Expose ports for all services
EXPOSE 3000 3001 3002 3003

# Default command for development
CMD ["npm", "run", "dev"]
