import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Suspense, lazy } from 'react'
import {
  CustomBox,
  CustomGrid,
  CustomTypography,
  CustomPaper,
  CustomCircularProgress,
  CustomAlert,
  CustomSecurityIcon,
  CustomDashboardIcon,
  CustomPersonIcon,
  CustomThemeProvider,
  appTheme,
} from '@app-front/ui-components'
import { AppLayout } from './components/Layout/AppLayout'
import { ModuleCard } from './components/ModuleCard/ModuleCard'

// Lazy load micro frontends
const AuthApp = lazy(() => import('auth/LoginPage'))

// Loading component
const LoadingFallback = () => (
  <CustomBox display="flex" justifyContent="center" alignItems="center" minHeight="200px">
    <CustomCircularProgress />
    <CustomTypography variant="body1" sx={{ ml: 2 }}>
      Loading micro frontend...
    </CustomTypography>
  </CustomBox>
)

// Placeholder components for micro frontends
const DashboardPlaceholder = () => (
  <CustomPaper sx={{ p: 3, border: '2px dashed', borderColor: 'grey.300' }}>
    <CustomAlert severity="info" sx={{ mb: 2 }}>
      <CustomTypography variant="h6">📊 Dashboard Module</CustomTypography>
    </CustomAlert>
    <CustomTypography variant="body1" paragraph>
      Dashboard micro frontend will be loaded here
    </CustomTypography>
    <CustomTypography variant="caption" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
      Module Federation: dashboard@http://localhost:3002
    </CustomTypography>
  </CustomPaper>
)

const ProfilePlaceholder = () => (
  <CustomPaper sx={{ p: 3, border: '2px dashed', borderColor: 'grey.300' }}>
    <CustomAlert severity="info" sx={{ mb: 2 }}>
      <CustomTypography variant="h6">👤 Profile Module</CustomTypography>
    </CustomAlert>
    <CustomTypography variant="body1" paragraph>
      Profile micro frontend will be loaded here
    </CustomTypography>
    <CustomTypography variant="caption" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
      Module Federation: profile@http://localhost:3003
    </CustomTypography>
  </CustomPaper>
)

const Home = () => (
  <CustomBox>
    <CustomTypography variant="h3" component="h1" gutterBottom align="center">
      🏠 Welcome to Micro Frontend Shell
    </CustomTypography>
    <CustomTypography variant="h6" color="text.secondary" paragraph align="center" sx={{ mb: 4 }}>
      This is the main container application that orchestrates all micro frontends using Module Federation.
    </CustomTypography>

    <CustomGrid container spacing={3}>
      <CustomGrid item xs={12} md={4}>
        <ModuleCard
          title="Authentication"
          description="Handle user login, registration, and authentication flows"
          path="/auth"
          icon={<CustomSecurityIcon />}
          status="active"
          port={3001}
        />
      </CustomGrid>
      <CustomGrid item xs={12} md={4}>
        <ModuleCard
          title="Dashboard"
          description="Main dashboard with analytics, charts, and data visualization"
          path="/dashboard"
          icon={<CustomDashboardIcon />}
          status="placeholder"
          port={3002}
        />
      </CustomGrid>
      <CustomGrid item xs={12} md={4}>
        <ModuleCard
          title="Profile"
          description="User profile management, settings, and preferences"
          path="/profile"
          icon={<CustomPersonIcon />}
          status="placeholder"
          port={3003}
        />
      </CustomGrid>
    </CustomGrid>
  </CustomBox>
)

function App() {
  return (
    <CustomThemeProvider theme={appTheme}>
      <Router>
        <AppLayout title="🏗️ Micro Frontend Shell">
          <Suspense fallback={<LoadingFallback />}>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/auth/*" element={<AuthApp />} />
              <Route path="/dashboard/*" element={<DashboardPlaceholder />} />
              <Route path="/profile/*" element={<ProfilePlaceholder />} />
            </Routes>
          </Suspense>
        </AppLayout>
      </Router>
    </CustomThemeProvider>
  )
}

export default App
