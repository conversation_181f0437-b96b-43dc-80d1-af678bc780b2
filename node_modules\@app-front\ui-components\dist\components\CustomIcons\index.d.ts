import React from 'react';
import { SvgIconProps } from '@mui/material';
export interface CustomIconProps extends SvgIconProps {
}
export declare const CustomVisibilityIcon: React.FC<CustomIconProps>;
export declare const CustomVisibilityOffIcon: React.FC<CustomIconProps>;
export declare const CustomEmailIcon: React.FC<CustomIconProps>;
export declare const CustomLockIcon: React.FC<CustomIconProps>;
export declare const CustomLoginIcon: React.FC<CustomIconProps>;
export declare const CustomGoogleIcon: React.FC<CustomIconProps>;
export declare const CustomGitHubIcon: React.FC<CustomIconProps>;
export { CustomVisibilityIcon as default } from './index';
