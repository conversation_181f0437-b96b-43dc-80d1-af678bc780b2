import React from 'react'
import {
  CustomBox,
  CustomContainer,
  CustomTypography,
  CustomAppBar,
} from '@app-front/ui-components'

interface AppLayoutProps {
  title: string
  children: React.ReactNode
}

export const AppLayout: React.FC<AppLayoutProps> = ({ title, children }) => {
  return (
    <CustomBox sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <CustomAppBar position="static" variant="elevated">
        <CustomContainer maxWidth="lg">
          <CustomBox sx={{ py: 2 }}>
            <CustomTypography variant="h5" component="h1" color="inherit">
              {title}
            </CustomTypography>
          </CustomBox>
        </CustomContainer>
      </CustomAppBar>
      
      <CustomContainer maxWidth="lg" sx={{ py: 4 }}>
        {children}
      </CustomContainer>
    </CustomBox>
  )
}

export default AppLayout
