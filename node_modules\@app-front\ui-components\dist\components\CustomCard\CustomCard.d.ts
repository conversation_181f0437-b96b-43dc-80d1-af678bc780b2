import React from 'react';
import { CardProps } from '@mui/material';
export interface CustomCardProps extends Omit<CardProps, 'variant'> {
    variant?: 'elevation' | 'outlined' | 'interactive' | 'feature' | 'minimal';
    title?: string;
    subtitle?: string;
    actions?: React.ReactNode;
    icon?: React.ReactNode;
    headerColor?: string;
}
export declare const CustomCard: React.FC<CustomCardProps>;
export default CustomCard;
